import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import RegistrationForm from './components/RegistrationForm';
import EmailVerification from './components/EmailVerification';
import SuccessPage from './components/SuccessPage';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<RegistrationForm />} />
          <Route path="/register" element={<RegistrationForm />} />
          <Route path="/verify-email/:token" element={<EmailVerification />} />
          <Route path="/success" element={<SuccessPage />} />
          <Route path="/registration-success" element={<SuccessPage type="registration" />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;

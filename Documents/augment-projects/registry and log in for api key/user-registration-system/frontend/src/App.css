/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
}

.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Common Button Styles */
.primary-button {
  background-color: #4A90E2;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.primary-button:hover {
  background-color: #357ABD;
  transform: translateY(-1px);
}

.primary-button:active {
  transform: translateY(0);
}

.primary-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  transform: none;
}

.secondary-button {
  background-color: transparent;
  color: #4A90E2;
  border: 2px solid #4A90E2;
  padding: 10px 22px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.secondary-button:hover {
  background-color: #4A90E2;
  color: white;
}

/* Common Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  background-color: white;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.form-group input.error,
.form-group select.error {
  border-color: #ff4757;
}

.error-text {
  display: block;
  color: #ff4757;
  font-size: 14px;
  margin-top: 4px;
}

.error-message {
  background-color: #ffe6e6;
  color: #d63031;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #d63031;
  margin-bottom: 20px;
}

.success-message {
  background-color: #e6f7e6;
  color: #00b894;
  padding: 12px 16px;
  border-radius: 6px;
  border-left: 4px solid #00b894;
  margin-bottom: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }

  .primary-button,
  .secondary-button {
    width: 100%;
    margin-bottom: 12px;
  }
}
